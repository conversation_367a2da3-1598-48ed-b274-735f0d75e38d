import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import type { User } from 'firebase/auth';
import { useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import { LogBox } from 'react-native';
import 'react-native-reanimated';

import { LanguageProvider } from '@/app/LanguageContext';
import { ThemeProvider, useAppTheme } from '@/app/ThemeContext';
import i18n from '@/i18n';

// Initialize Firebase
import auth from './firebase/config';

// Ignore specific shadow-related warning from expo-router
LogBox.ignoreLogs(['"shadow*" style props are deprecated']);

function RootLayoutContent() {
  const { colorScheme } = useAppTheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [isFirebaseReady, setIsFirebaseReady] = useState(false);

  useEffect(() => {
    // Check if Firebase Auth is ready by attempting to get the current user
    const unsubscribe = auth.onAuthStateChanged((user: User | null) => {
      setIsFirebaseReady(true);
    });

    return () => unsubscribe();
  }, []);

  if (!loaded || !isFirebaseReady) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <NavigationThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
    </NavigationThemeProvider>
  );
}

export default function RootLayout() {
  return (
    <I18nextProvider i18n={i18n}>
      <LanguageProvider>
        <ThemeProvider>
          <RootLayoutContent />
        </ThemeProvider>
      </LanguageProvider>
    </I18nextProvider>
  );
}
